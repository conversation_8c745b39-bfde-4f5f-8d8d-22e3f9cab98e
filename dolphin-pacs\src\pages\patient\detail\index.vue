<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { useRoute } from "vue-router"
import { ElMessage } from "element-plus"
import type { PatientInfo } from "../management/types"
import { getPatientInfoApi } from "@/common/apis/patients"
import { transformPatientData } from "../management/utils/dataTransform"
import ChatSidebar from "./components/ChatSidebar.vue"
import VideoPlayer from "./components/VideoPlayer.vue"
import UltrasoundCanvas from "./components/UltrasoundCanvas.vue"
import ImageAnalysis from "./components/ImageAnalysis.vue"


defineOptions({
  name: "PatientDetail"
})

const route = useRoute()

// 患者详情数据
const patientDetail = ref<PatientInfo | null>(null)
const loading = ref(false)

// 当前激活的标签页
const activeTab = ref('medical-record')

// 患者信息区域收缩状态
const isPatientInfoCollapsed = ref(false)

// 图像分析相关状态
const currentAnalysisImage = ref<any>(null)

// 获取患者ID
const patientId = route.params.id as string

// 获取患者详情数据
const fetchPatientDetail = async () => {
  if (!patientId) {
    ElMessage.error('患者ID不能为空')
    return
  }

  loading.value = true
  try {
    const response = await getPatientInfoApi(patientId)
    console.log('API响应:', response) // 调试日志
    if (response.data) {
      console.log('患者数据:', response.data) // 调试日志
      patientDetail.value = transformPatientData(response.data)
      console.log('转换后的数据:', patientDetail.value) // 调试日志
    } else {
      console.log('数据结构异常:', response) // 调试日志
      ElMessage.error('获取患者详情失败')
    }
  } catch (error) {
    console.error('获取患者详情失败:', error)
    ElMessage.error('获取患者详情失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 处理图像分析事件
const handleAnalyzeImage = (imageData: any) => {
  currentAnalysisImage.value = imageData
  activeTab.value = 'image-analysis'
}

onMounted(() => {
  fetchPatientDetail()
})
</script>

<template>
  <div class="patient-detail">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 患者详情内容 -->
    <div v-else-if="patientDetail" class="detail-content">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 患者信息区域 -->
        <div class="patient-info-section" :class="{ 'collapsed': isPatientInfoCollapsed }">
          <!-- 收缩控制头部 -->
          <div class="info-header" @click="isPatientInfoCollapsed = !isPatientInfoCollapsed">
            <div class="header-left">
              <h3 class="section-title">患者信息</h3>
              <div class="quick-info" v-if="isPatientInfoCollapsed">
                <span class="quick-item">{{ patientDetail.name }}</span>
                <span class="quick-item">{{ patientDetail.gender }}</span>
                <span class="quick-item">{{ patientDetail.age }}岁</span>
                <span class="quick-item">{{ patientDetail.department }}</span>
              </div>
            </div>
            <div class="header-right">
              <span class="collapse-icon" :class="{ 'rotated': isPatientInfoCollapsed }">
                ▼
              </span>
            </div>
          </div>

          <!-- 详细信息内容 -->
          <el-collapse-transition>
            <div v-show="!isPatientInfoCollapsed" class="info-content">
              <!-- 第一行：基本信息 -->
              <el-row :gutter="20" class="info-row">
                <el-col :xl="6" :lg="8" :md="12" :sm="12" :xs="24">
                  <div class="form-field">
                    <label class="field-label">姓名：</label>
                    <div class="field-input" :class="{ 'placeholder': !patientDetail.name }">
                      {{ patientDetail.name || '请输入姓名' }}
                    </div>
                  </div>
                </el-col>
                <el-col :xl="6" :lg="8" :md="12" :sm="12" :xs="24">
                  <div class="form-field">
                    <label class="field-label">性别：</label>
                    <div class="field-input" :class="{ 'placeholder': !patientDetail.gender }">
                      {{ patientDetail.gender || '请选择性别' }}
                    </div>
                  </div>
                </el-col>
                <el-col :xl="6" :lg="8" :md="12" :sm="12" :xs="24">
                  <div class="form-field">
                    <label class="field-label">年龄：</label>
                    <div class="field-input" :class="{ 'placeholder': !patientDetail.age }">
                      {{ patientDetail.age ? patientDetail.age + '岁' : '请输入年龄' }}
                    </div>
                  </div>
                </el-col>
                <el-col :xl="6" :lg="8" :md="12" :sm="12" :xs="24">
                  <div class="form-field">
                    <label class="field-label">病案号：</label>
                    <div class="field-input placeholder">请输入病案号</div>
                  </div>
                </el-col>
              </el-row>

              <!-- 第二行：检查信息 -->
              <el-row :gutter="20" class="info-row">
                <el-col :xl="6" :lg="8" :md="12" :sm="12" :xs="24">
                  <div class="form-field">
                    <label class="field-label">病人ID：</label>
                    <div class="field-input" :class="{ 'placeholder': !patientDetail.id }">
                      {{ patientDetail.id || '请输入病人ID' }}
                    </div>
                  </div>
                </el-col>
                <el-col :xl="6" :lg="8" :md="12" :sm="12" :xs="24">
                  <div class="form-field">
                    <label class="field-label">科室：</label>
                    <div class="field-input" :class="{ 'placeholder': !patientDetail.department }">
                      {{ patientDetail.department || '请选择科室' }}
                    </div>
                  </div>
                </el-col>
                <el-col :xl="6" :lg="8" :md="12" :sm="12" :xs="24">
                  <div class="form-field">
                    <label class="field-label">床号：</label>
                    <div class="field-input placeholder">请输入床号</div>
                  </div>
                </el-col>
                <el-col :xl="6" :lg="8" :md="12" :sm="12" :xs="24">
                  <div class="form-field">
                    <label class="field-label">仪器：</label>
                    <div class="field-input placeholder">请选择仪器</div>
                  </div>
                </el-col>
              </el-row>

              <!-- 第三行：其他信息 -->
              <el-row :gutter="20" class="info-row">
                <el-col :xl="6" :lg="8" :md="12" :sm="12" :xs="24">
                  <div class="form-field">
                    <label class="field-label">检查机号：</label>
                    <div class="field-input placeholder">请输入检查机号</div>
                  </div>
                </el-col>
                <el-col :xl="6" :lg="8" :md="12" :sm="12" :xs="24">
                  <div class="form-field">
                    <label class="field-label">检查项目：</label>
                    <div class="field-input" :class="{ 'placeholder': !patientDetail.examType }">
                      {{ patientDetail.examType || '请选择检查项目' }}
                    </div>
                  </div>
                </el-col>
                <el-col :xl="6" :lg="8" :md="12" :sm="12" :xs="24">
                  <div class="form-field">
                    <label class="field-label">出生日期：</label>
                    <div class="field-input placeholder">请选择出生日期</div>
                  </div>
                </el-col>
                <el-col :xl="6" :lg="8" :md="12" :sm="12" :xs="24">
                  <div class="form-field">
                    <label class="field-label">联系电话：</label>
                    <div class="field-input placeholder">请输入联系电话</div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-collapse-transition>
        </div>

        <!-- 标签页区域 -->
        <div class="tabs-section">
          <el-tabs v-model="activeTab" class="detail-tabs">
            <el-tab-pane label="患者病历" name="medical-record">
              <div class="tab-content">
                <!-- 患者病历内容区域 -->
                <div class="content-placeholder">
                  患者病历内容区域
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="检查报告" name="examination-report">
              <div class="tab-content">
                <!-- 检查报告内容区域 -->
                <div class="content-placeholder">
                  检查报告内容区域
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="超声视频" name="ultrasound-video">
              <div class="tab-content">
                <!-- 超声视频内容区域 -->
                <VideoPlayer />
              </div>
            </el-tab-pane>
            <el-tab-pane label="超声图像" name="ultrasound-image">
              <div class="tab-content tab-content-full-height">
                <!-- 超声图像内容区域 -->
                <UltrasoundCanvas />
              </div>
            </el-tab-pane>

          </el-tabs>
        </div>
      </div>

      <!-- 右侧聊天区域 -->
      <ChatSidebar />
    </div>

    <!-- 数据为空状态 -->
    <div v-else class="empty-state">
      <el-empty description="未找到患者信息" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.patient-detail {
  padding: 20px;
  min-height: calc(100vh - 84px);
  background-color: var(--el-bg-color-page);

  .loading-container {
    background: var(--el-bg-color);
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .detail-content {
    display: flex;
    gap: 20px;
    height: calc(100vh - 120px);

    // 主要内容区域
    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      // 患者信息区域
      .patient-info-section {
        border-radius: 8px;
        margin-bottom: 20px;
        background: var(--el-bg-color);
        flex-shrink: 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        // 收缩控制头部
        .info-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 16px 20px;
          background: var(--el-color-primary-light-9);
          border-radius: 8px 8px 0 0;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: var(--el-color-primary-light-8);
          }

          .header-left {
            display: flex;
            align-items: center;
            gap: 20px;

            .section-title {
              margin: 0;
              font-size: 16px;
              font-weight: 600;
              color: var(--el-color-primary);
            }

            .quick-info {
              display: flex;
              align-items: center;
              gap: 16px;

              .quick-item {
                font-size: 14px;
                color: var(--el-text-color-regular);
                background: var(--el-bg-color);
                padding: 4px 8px;
                border-radius: 4px;
                border: 1px solid var(--el-border-color-light);
              }
            }
          }

          .header-right {
            .collapse-icon {
              font-size: 14px;
              color: var(--el-color-primary);
              transition: transform 0.3s ease;

              &.rotated {
                transform: rotate(-90deg);
              }
            }
          }
        }

        // 收缩状态样式
        &.collapsed {
          .info-header {
            border-radius: 8px;
          }
        }

        // 详细信息内容
        .info-content {
          padding: 20px;

          .info-row {
            margin-bottom: 8px;

            &:last-child {
              margin-bottom: 0;
            }
          }

          .form-field {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            height: 36px;

            .field-label {
              font-size: 14px;
              color: var(--el-text-color-primary);
              font-weight: 500;
              min-width: 80px;
              margin-right: 12px;
              text-align: right;
              flex-shrink: 0;
            }

            .field-input {
              flex: 1;
              height: 32px;
              line-height: 32px;
              padding: 0 12px;
              border: 1px solid var(--el-border-color);
              border-radius: 4px;
              background-color: var(--el-bg-color);
              font-size: 14px;
              color: var(--el-text-color-primary);
              transition: all 0.3s ease;
              display: flex;
              align-items: center;

              &:hover {
                border-color: var(--el-color-primary-light-5);
              }

              &:focus {
                border-color: var(--el-color-primary);
                outline: none;
                box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
              }

              // 占位符样式
              &.placeholder {
                color: var(--el-text-color-placeholder);
              }
            }
          }
        }
      }

      // 标签页区域
      .tabs-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        background: var(--el-bg-color);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .detail-tabs {
          height: 100%;
          display: flex;
          flex-direction: column;

          :deep(.el-tabs__header) {
            margin-bottom: 0;
            border-bottom: 1px solid var(--el-border-color-light);
            flex-shrink: 0;
            background: var(--el-bg-color);
            border-radius: 8px 8px 0 0;
          }

          :deep(.el-tabs__nav-wrap) {
            padding: 0 20px;
          }

          :deep(.el-tabs__item) {
            font-size: 16px;
            font-weight: 500;
            padding: 0 30px;
            height: 50px;
            line-height: 50px;
            border-radius: 8px 8px 0 0;
          }

          :deep(.el-tabs__content) {
            flex: 1;
            overflow: hidden;
            background: var(--el-bg-color);
          }

          // 确保标签页面板占据全部高度
          :deep(.el-tab-pane) {
            height: 100%;
            display: flex;
            flex-direction: column;
          }

          .tab-content {
            padding: 0;
            height: 100%;
            background: var(--el-bg-color);
            overflow-y: auto;

            .content-placeholder {
              display: flex;
              align-items: center;
              justify-content: center;
              height: 100%;
              min-height: 300px;
              font-size: 16px;
              color: var(--el-text-color-regular);
              background: var(--el-bg-color);
              border: 1px solid var(--el-border-color-light);
              margin: 0;
            }
          }

          // 超声图像标签页特殊样式 - 占据全部高度
          .tab-content-full-height {
            padding: 0 !important;
            height: 100% !important;
            overflow: hidden !important;
            display: flex !important;
            flex-direction: column !important;
          }
        }
      }
    }


  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background: var(--el-bg-color);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .patient-detail {
    padding: 10px;

    .detail-content {
      flex-direction: column;
      height: auto;

      .main-content {
        .patient-info-section {
          margin-bottom: 15px;

          .info-header {
            padding: 12px 16px;

            .header-left {
              gap: 12px;

              .section-title {
                font-size: 14px;
              }

              .quick-info {
                gap: 8px;

                .quick-item {
                  font-size: 12px;
                  padding: 2px 6px;
                }
              }
            }
          }

          .info-content {
            padding: 15px;

            .info-row {
              margin-bottom: 6px;
            }

            .form-field {
              height: 32px;
              margin-bottom: 6px;

              .field-label {
                font-size: 12px;
                min-width: 70px;
                margin-right: 8px;
              }

              .field-input {
                height: 28px;
                line-height: 28px;
                padding: 0 8px;
                font-size: 12px;
              }
            }
          }
        }

        .tabs-section {
          .detail-tabs {
            :deep(.el-tabs__nav-wrap) {
              padding: 0 10px;
            }

            :deep(.el-tabs__item) {
              font-size: 14px;
              padding: 0 15px;
              height: 45px;
              line-height: 45px;
            }

            .tab-content {
              padding: 0;
              min-height: 400px;

              .content-placeholder {
                height: 100%;
                min-height: 350px;
                font-size: 14px;
                margin: 0;
              }
            }
          }
        }
      }


    }
  }
}
</style>
